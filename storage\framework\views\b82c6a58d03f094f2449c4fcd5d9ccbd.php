<?php $__env->startSection('title', 'Dashboard'); ?>
<?php $__env->startSection('content'); ?>

<?php
$user = auth()->user();
?>

<?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>

<div class="data_box row">
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Franchisees</h5>
                <span><?php echo e(number_format($numFranchisees,2)); ?></span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span><?php echo e(number_format($numProjects,2)); ?></span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span><?php echo e(number_format($completedNumProjects,2)); ?></span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Project Revenue
                </h5>
                <span>$ <?php echo e(number_format($totalRevenue, 2)); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5> Completed Projects Revenue</h5>
                <span>$ <?php echo e(number_format($completedTotalRevenue, 2)); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>




</div>

<div class="heading_box_two d-flex w-100 mb-0">
    <h3>Lead Conversion Rate</h3>
</div>

<div class="data_box row">
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Prospects</h5>
                <span><?php echo e(number_format($totalLeads,2)); ?></span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Prospect Conversion Rate</h5>
                <span><?php echo e($saleMadeRatio); ?> %</span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>




</div>


<div class="heading_box_two d-flex w-100">
    <h3>Franchisee Graphs</h3>
</div>

<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="franchiseeProjectChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="countryWiseFranchiseChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="stateWiseFranchiseChart" class="w-100 h-100"></canvas>
    </div>

</div>

<div class="heading_box_two d-flex w-100">
    <h3>Project Graphs</h3>
</div>

<div class="graphs row">

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="countryWiseProjectChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="stateWiseProjectChart" class="w-100 h-100"></canvas>
    </div>

</div>

<div class="heading_box_two d-flex w-100">
    <h3>Revenue Graphs</h3>
</div>


<div class="graphs row">


    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="countryWiseRevenueChart" class="w-100 h-100"></canvas>
    </div>

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="stateWiseRevenueChart" class="w-100 h-100"></canvas>
    </div>
</div>

<hr />

<div class="heading_box_two d-flex w-100">
    <h3>Search by Franchisee</h3>
</div>

<form method="POST" id="admin_filter_data" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>

    <div class="form_field_group row">
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Franchise</span>
            <select name="franchise">
                <option selected disabled>select franchise</option>
                <?php $__currentLoopData = $franchisees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $franchisee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($franchisee->id); ?>"><?php echo e($franchisee->franchisee_name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
            <label for="franchise" generated="true" class="error"></label>
        </div>
        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Date (optional)</span>

            <input type="text" placeholder="Select date range" name="date_range" class="dateRangeInput">

            <label for="date_range" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-12 col-lg-12">
            <div class="form_button d-flex w-100 justify-content-end"><button type="submit">Filter</button></div>
        </div>


    </div>
</form>


<div class="data_box row">

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Country</h5>
                <span id="franchiseeCountry"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>State</h5>
                <span id="franchiseeState"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>


    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span id="totalProjects"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span id="completedProjects"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span id="totalRevenue"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span id="completedRevenue"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
</div>


<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">
        <div class="heading_box_two d-flex w-100">
            <h3>Total Project Revenue
            </h3>
        </div>

        <canvas id="allProjectChart" class="w-100 h-100"></canvas>
    </div>


    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">

        <div class="heading_box_two d-flex w-100">
            <h3>Completed Projects Revenue</h3>
        </div>

        <canvas id="completedProjectChart" class="w-100 h-100"></canvas>



    </div>

</div>



<?php endif; ?>

<?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?>



<div class="heading_box_two d-flex w-100 mb-0">
    <h3>Lead Conversion Rate</h3>
</div>

<div class="data_box row">

    <div class="single_box col-12 col-md-6 col-lg-6 ">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Lead Conversion Rate</h5>
                <span> <?php echo e($saleMadeRatio); ?> % </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
</div>

<div class="data_box row">

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Country</h5>
                <span> <?php echo e($franchisees?->country?->name ?? 'N/A'); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>State</h5>
                <span> <?php echo e($franchisees?->state?->name ?? 'N/A'); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>


    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span> <?php echo e(number_format($NumProjects,2)); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span> <?php echo e(number_format($completedProjectCount,2)); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span>$ <?php echo e(number_format($totalRevenue,2)); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span>$ <?php echo e(number_format($completedTotalRevenue, 2)); ?> </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
</div>


<div class="heading_box_two d-flex w-100">
    <h3>Revenue Graphs</h3>
</div>

<div class="graphs row">

    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="franchiseeAllProjectChart" class="w-100 h-100"></canvas>
    </div>
    <div class="single_graph col-12 col-md-6 col-lg-6">
        <canvas id="franchiseeCompletedProjectChart" class="w-100 h-100"></canvas>
    </div>


</div>

<div class="heading_box_two d-flex w-100">
    <h3>Filter Revenue Record</h3>
</div>

<form method="POST" id="franchisee_filter_data" enctype="multipart/form-data">
    <?php echo csrf_field(); ?>

    <div class="form_field_group row">

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <span>Select Date Range</span>

            <input type="text" placeholder="Select date range" name="date_range" class="dateRangeInput">

            <label for="date_range" generated="true" class="error"></label>
        </div>

        <div class="child_field form_field position-relative col-12 col-md-6 col-lg-6">
            <div class="form_button d-flex w-100 justify-content-start"><button type="submit">Filter</button></div>
        </div>


    </div>
</form>


<div class="data_box row">



    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Projects</h5>
                <span id="totalProjects"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects</h5>
                <span id="completedProjects"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Total Revenue from Projects</h5>
                <span id="totalRevenue"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>

    <div class="single_box col-12 col-md-6 col-lg-4">
        <div class="single_box_data w-100 d-flex align-items-center justify-content-between">
            <strong class="d-flex flex-column">
                <h5>Completed Projects Revenue</h5>
                <span id="completedRevenue"> - </span>
            </strong>
            <img lazy="loading" src="<?php echo e(asset('images/dummy.webp')); ?>">
        </div>
    </div>
</div>


<div class="graphs row">
    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">
        <div class="heading_box_two d-flex w-100">
            <h3>Total Project Revenue
            </h3>
        </div>

        <canvas id="allProjectChart" class="w-100 h-100"></canvas>
    </div>


    <div class="single_graph col-12 col-md-6 col-lg-6 d-flex flex-column">

        <div class="heading_box_two d-flex w-100">
            <h3>Completed Projects Revenue</h3>
        </div>

        <canvas id="completedProjectChart" class="w-100 h-100"></canvas>



    </div>

</div>





<?php endif; ?>




<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\bathroom ranovator dashboard portal\bathroom-ranovator-dashboard-portal\resources\views/dashboard/index.blade.php ENDPATH**/ ?>