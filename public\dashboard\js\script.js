function showAlert(message, type) {
    $(".alert-box p").text(message);
    $(".alert-box").addClass(`alert-${type} fade show`);
    $("#alert-container").addClass(`show`);

    // Automatically hide the alert after 3 seconds
    setTimeout(function () {
        $("#alert-container").removeClass(`show`);
        setTimeout(function () {
            $("#alert-container .alert-box").removeClass(`alert-${type}`);
        }, 1000);
    }, 3000);
}

document.addEventListener("DOMContentLoaded", function () {
    document.querySelectorAll(".read-toggle").forEach(function (button) {
        button.addEventListener("click", function () {
            const td = this.closest("td");
            const shortNote = td.querySelector(".short-note");
            const fullNote = td.querySelector(".full-note");

            if (shortNote.classList.contains("d-none")) {
                shortNote.classList.remove("d-none");
                fullNote.classList.add("d-none");
                this.textContent = "Read more";
            } else {
                shortNote.classList.add("d-none");
                fullNote.classList.remove("d-none");
                this.textContent = "Read less";
            }
        });
    });
});

// Reset all Dropify fields
function resetDropify() {
    $(".dropify").each(function () {
        var dropifyInstance = $(this).dropify();
        dropifyInstance.data("dropify").clearElement();
    });
}

$(document).ready(function () {
    $(".sidebar_toggle_btn").click(function () {
        $(".dashboard_sidebar").toggleClass("active");
        $(".content_section").toggleClass("active");
    });

    $(".multiple-select").select2({
        theme: "bootstrap-5",
        width: $(this).data("width")
            ? $(this).data("width")
            : $(this).hasClass("w-100")
            ? "100%"
            : "style",
        placeholder: $(this).data("placeholder"),
        closeOnSelect: false,
        allowClear: true,
    });

    $(function () {
        $(".dateRangeInput").daterangepicker({
            autoUpdateInput: false, // prevent auto-filling
            locale: {
                format: "DD MMM YYYY",
                cancelLabel: "Clear",
            },
        });

        // When user selects a range, update input
        $(".dateRangeInput").on("apply.daterangepicker", function (ev, picker) {
            $(this).val(
                picker.startDate.format("DD MMM YYYY") +
                    " - " +
                    picker.endDate.format("DD MMM YYYY")
            );
        });

        // When user clicks "Clear", clear the input
        $(".dateRangeInput").on(
            "cancel.daterangepicker",
            function (ev, picker) {
                $(this).val("");
            }
        );
    });

    $(".dropify").dropify({
        messages: {
            default: "File",
            replace: "Drag and drop or click to replace",
            remove: "Remove",
            error: "Ooops, something wrong happended.",
        },
    });
    (function () {
        document.querySelectorAll(".tagify").forEach(function (input) {
            new Tagify(input);
        });
    })();

    $(".toggle-password").click(function () {
        // Find the corresponding password input field
        const passwordInput = $(this).siblings(".password-field");

        // Toggle the type attribute
        const type =
            passwordInput.attr("type") === "password" ? "text" : "password";
        passwordInput.attr("type", type);

        // Toggle the icon classes
        $(this).toggleClass("fa-eye fa-eye-slash");
    });
});

$(document).ready(function () {
    // Function to initialize DataTables
    function initializeDataTables() {
        $(".dynamic_datatable").each(function () {
            $(this)
                .DataTable({
                    buttons: ["copy", "csv", "excel", "pdf", "print"],
                    // Other DataTable options can be added here
                })
                .buttons()
                .container()
                .appendTo(
                    $(this)
                        .closest(".dataTables_wrapper")
                        .find(".col-md-6:eq(0)")
                );
        });
    }

    // Call the function to initialize all tables
    initializeDataTables();
});

function copyToClipboard(url) {
    navigator.clipboard.writeText(url).then(
        function () {
            alert("file URL copied to clipboard!");
        },
        function (err) {
            alert("Could not copy text: ", err);
            console.error("Could not copy text: ", err);
        }
    );
}

// Ensure that Bootstrap's JavaScript file is correctly loaded
document.addEventListener("DOMContentLoaded", function () {
    var galleryModal = new bootstrap.Modal(
        document.getElementById("galleryModal")
    );

    // Event delegation for all buttons with the 'open-gallery' class
    document.body.addEventListener("click", function (event) {
        var targetButton = event.target.closest(".open_gallery");
        if (targetButton) {
            event.preventDefault();

            // Get the value of the data-type attribute (e.g., 'logo' or 'favicon')
            var type = targetButton.getAttribute("data-type");

            // Call the appropriate function based on the data-type value
            if (type === "galleryView") {
                // console.log("Logo gallery button clicked");
                fetchGalleryImages("logo"); // Custom logic for logo gallery
            }
            galleryModal.show(); // Show the modal after fetching images
        }
    });
});

// Function to fetch and display gallery images
function fetchGalleryImages() {
    var gallery = document.querySelector("#galleryModal .gallery");

    // Fetch images from the server (use AJAX)
    fetch("/api/dashboard/gallery/files")
        .then((response) => response.json())
        .then((files) => {
            //   console.log(files);
            gallery.innerHTML = ""; // Clear existing gallery content

            Object.values(files).forEach((fileUrl) => {
                // Get file extension
                let fileExtension = fileUrl.split(".").pop().toLowerCase();

                // Create the outer div with class 'single_file'
                var singleFileDiv = document.createElement("div");
                singleFileDiv.className = "single_file d-grid gap-2";

                // Create the figure element
                var figure = document.createElement("figure");
                figure.className =
                    "w-100 d-flex align-items-center justify-content-center";

                // Create the appropriate file preview
                if (
                    ["jpg", "jpeg", "png", "webp", "gif"].includes(
                        fileExtension
                    )
                ) {
                    // Image preview
                    var imgElement = document.createElement("img");
                    imgElement.src = fileUrl;
                    imgElement.className = "w-100 h-100 object-fit-contain";
                    figure.appendChild(imgElement);
                } else if (fileExtension === "pdf") {
                    // PDF preview
                    var pdfPreview = document.createElement("embed");
                    pdfPreview.src = fileUrl;
                    pdfPreview.className = "w-100 h-100";
                    pdfPreview.type = "application/pdf";
                    pdfPreview.style.height = "150px"; // Adjust height for preview
                    figure.appendChild(pdfPreview);
                } else if (
                    ["doc", "docx", "excel", "csv"].includes(fileExtension)
                ) {
                    // DOC/DOCX preview (Link to open)
                    var docPreview = document.createElement("a");
                    docPreview.href = fileUrl;
                    docPreview.target = "_blank";
                    docPreview.className =
                        "d-flex align-items-center justify-content-center";
                    docPreview.innerHTML = `<i class="fas fa-file-word fa-3x text-primary"></i>`;
                    figure.appendChild(docPreview);
                } else {
                    // Unknown file type, show a generic link
                    var fileLink = document.createElement("a");
                    fileLink.href = fileUrl;
                    fileLink.target = "_blank";
                    fileLink.innerHTML = "View File";
                    figure.appendChild(fileLink);
                }

                // Create the div for gallery action buttons
                var actionBtnsDiv = document.createElement("div");
                actionBtnsDiv.className =
                    "gallery_action_btns d-flex align-items-center justify-content-center";

                // Create the anchor element for copy link
                var copyLinkAnchor = document.createElement("a");
                copyLinkAnchor.className =
                    "d-flex align-items-center justify-content-center";
                copyLinkAnchor.onclick = function () {
                    copyToClipboard(fileUrl);
                };

                // Create the icon element for the copy link
                var iconElement = document.createElement("i");
                iconElement.className = "fas fa-link";

                // Append the icon to the anchor
                copyLinkAnchor.appendChild(iconElement);

                // Append the anchor to the action buttons div
                actionBtnsDiv.appendChild(copyLinkAnchor);

                // Append the figure and action buttons div to the outer div
                singleFileDiv.appendChild(figure);
                singleFileDiv.appendChild(actionBtnsDiv);

                // Append the outer div to the gallery
                gallery.appendChild(singleFileDiv);
            });
        })
        .catch((error) => console.error("Error fetching files:", error));
}

function updateImagePreview(inputElement) {
    var previewId = inputElement.getAttribute("data-preview");
    var errorLabelId = inputElement.getAttribute("data-error");

    var fileUrl = inputElement.value.trim();
    var previewContainer = document.getElementById(previewId);
    var errorLabel = document.getElementById(errorLabelId);

    // Get the parent figure element
    var figureContainer = previewContainer.closest("figure");

    // Clear previous errors
    errorLabel.textContent = "";

    if (!fileUrl) {
        errorLabel.textContent = "Please enter a valid file URL.";
        return;
    }

    // Get file extension
    var fileExtension = fileUrl.split(".").pop().toLowerCase();

    // Supported formats
    var imageExtensions = ["jpg", "jpeg", "png", "webp", "gif"];
    var pdfExtensions = ["pdf"];
    var docExtensions = ["doc", "docx", "csv", "excel"];

    // Remove previous content
    figureContainer.innerHTML = "";

    if (imageExtensions.includes(fileExtension)) {
        // Image Preview
        var imgElement = document.createElement("img");
        imgElement.src = fileUrl;
        imgElement.className = "w-100 h-100 object-fit-contain";
        imgElement.alt = "preview image";

        imgElement.onload = function () {
            errorLabel.textContent = ""; // Clear error if image loads successfully
        };

        imgElement.onerror = function () {
            errorLabel.textContent = "Invalid image URL.";
            figureContainer.innerHTML = ""; // Clear preview on error
        };

        figureContainer.appendChild(imgElement);
    } else if (pdfExtensions.includes(fileExtension)) {
        // PDF Preview
        var pdfEmbed = document.createElement("embed");
        pdfEmbed.src = fileUrl;
        pdfEmbed.className = "w-100";
        pdfEmbed.type = "application/pdf";
        //pdfEmbed.style.height = "200px"; // Adjust height for better preview

        figureContainer.appendChild(pdfEmbed);
    } else if (docExtensions.includes(fileExtension)) {
        // DOC/DOCX Preview as a Downloadable Link
        var docLink = document.createElement("a");
        docLink.href = fileUrl;
        docLink.target = "_blank";
        docLink.className = "d-flex align-items-center justify-content-center";
        docLink.innerHTML = `<i class="fas fa-file-word fa-3x text-primary"></i> Click to View Document`;

        figureContainer.appendChild(docLink);
    } else {
        // Generic file link
        var fileLink = document.createElement("a");
        fileLink.href = fileUrl;
        fileLink.target = "_blank";
        fileLink.innerHTML = "View File";

        figureContainer.appendChild(fileLink);
    }
}

// Attach event listeners for input, paste, and change events
document.querySelectorAll("input[data-preview]").forEach((input) => {
    input.addEventListener("input", function () {
        updateImagePreview(this);
    });

    input.addEventListener("paste", function () {
        // Use a timeout to allow the paste event to complete before updating
        setTimeout(() => {
            updateImagePreview(this);
        }, 0);
    });

    input.addEventListener("change", function () {
        updateImagePreview(this);
    });
});

function openPreview(fileUrl, fileType) {
    let previewContainer = document.getElementById("previewContainer");
    previewContainer.innerHTML = ""; // Clear previous content

    if (fileType === "image") {
        previewContainer.innerHTML = `<img src="${fileUrl}" class="img-fluid" alt="Preview Image">`;
    } else if (fileType === "pdf") {
        previewContainer.innerHTML = `<embed src="${fileUrl}" class="w-100" style="height: 500px;" type="application/pdf">`;
    } else if (fileType === "doc") {
        previewContainer.innerHTML = `<a href="${fileUrl}" target="_blank" class="btn btn-primary">
                                        <i class="fas fa-file-word"></i> Open Document
                                      </a>`;
    } else {
        previewContainer.innerHTML = `<a href="${fileUrl}" target="_blank" class="btn btn-secondary">View File</a>`;
    }

    // Show Bootstrap 5 modal
    let previewModal = new bootstrap.Modal(
        document.getElementById("previewModal")
    );
    previewModal.show();
}

const currentPath = window.location.pathname; // Get the path after the domain
if (currentPath === "/dashboard/home" || currentPath === "/") {
    document.addEventListener("DOMContentLoaded", function () {
        fetch("/api/chart/data")
            .then((response) => response.json())
            .then((data) => {
                // Check if admin data exists
                if (data.adminStats !== undefined) {
                    adminChartData(data.adminStats);
                }
                // Check if franchisee data exists
                if (data.franchiseStats !== undefined) {
                    franchiseAllProjectRevenueChart(
                        data.franchiseStats.all_project_chart_data
                    );
                    franchiseCompletedProjectRevenueChart(
                        data.franchiseStats.completed_project_chart_data
                    );
                }
            })
            .catch((error) =>
                console.error("Error fetching chart data:", error)
            );
    });

    //////////admin charts start

    function adminChartData(data) {
        franchiseeProjectChart(data.franchiseeProjectStats);
        countryWiseFranchiseChart(data.countryWiseFranchisees);
        stateWiseFranchiseChart(data.stateWiseFranchisees);
        countryWiseProjectChart(data.countryWiseProjects);
        stateWiseProjectChart(data.stateWiseProjects);
        countryWiseRevenueChart(data.countryWiseRevenue);
        stateWiseRevenueChart(data.stateWiseRevenue);
    }

    function franchiseeProjectChart(data) {
        const ctx = document
            .getElementById("franchiseeProjectChart")
            .getContext("2d");

        const labels = data.map((item) => item.franchisee_name);
        const projectCounts = data.map((item) => item.project_count);

        new Chart(ctx, {
            // type: "pie",
            // type:'doughnut',
            //  type:"bar",
            // type:"polarArea",
            //  type: "line",
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Project Count",
                        data: projectCounts,
                        backgroundColor: "rgba(0, 85, 165, 0.6)",
                        borderColor: "#0055a5",
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Franchisee Project Count",
                        font: { size: 16 },
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: "Number of Projects",
                        },
                        ticks: {
                            stepSize: 1,
                            callback: function (value) {
                                return Number.isInteger(value) ? value : "";
                            },
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Franchisees",
                        },
                    },
                },
            },
        });
    }
    function countryWiseFranchiseChart(data) {
        const ctx = document
            .getElementById("countryWiseFranchiseChart")
            .getContext("2d");
        const labels = data.map((item) => item.country);
        const counts = data.map((item) => item.franchisee_count);

        new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Franchisees",
                        data: counts,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Divisional Franchisee Count",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0, // ✅ Force whole numbers only
                            stepSize: 1,
                            callback: function (value) {
                                return Number.isInteger(value) ? value : "";
                            },
                        },
                        title: {
                            display: true,
                            text: "Franchisees",
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Regions",
                        },
                    },
                },
            },
        });
    }

    function stateWiseFranchiseChart(data) {
        const ctx = document
            .getElementById("stateWiseFranchiseChart")
            .getContext("2d");
        const labels = data.map((item) => item.state);
        const counts = data.map((item) => item.franchisee_count);

        new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Franchisees",
                        data: counts,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Regional Franchisee Count",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0, // ✅ Force whole numbers only
                            stepSize: 1,
                            callback: function (value) {
                                return Number.isInteger(value) ? value : "";
                            },
                        },
                        title: {
                            display: true,
                            text: "Franchisees",
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Regions",
                        },
                    },
                },
            },
        });
    }

    function countryWiseProjectChart(data) {
        const ctx = document
            .getElementById("countryWiseProjectChart")
            .getContext("2d");
        const labels = data.map((item) => item.country);
        const counts = data.map((item) => item.project_count);

        new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Projects",
                        data: counts,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Divisional Project Count",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0, // ✅ Force whole numbers only
                            stepSize: 1,
                            callback: function (value) {
                                return Number.isInteger(value) ? value : "";
                            },
                        },
                        title: {
                            display: true,
                            text: "Number of Projects",
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Regions",
                        },
                    },
                },
            },
        });
    }

    function stateWiseProjectChart(data) {
        const ctx = document
            .getElementById("stateWiseProjectChart")
            .getContext("2d");
        const labels = data.map((item) => item.state);
        const counts = data.map((item) => item.project_count);

        new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Projects",
                        data: counts,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Regional Project Count",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0, // ✅ Force whole numbers only
                            stepSize: 1,
                            callback: function (value) {
                                return Number.isInteger(value) ? value : "";
                            },
                        },
                        title: {
                            display: true,
                            text: "Number of Projects",
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Regions",
                        },
                    },
                },
            },
        });
    }

    function countryWiseRevenueChart(data) {
        const ctx = document
            .getElementById("countryWiseRevenueChart")
            .getContext("2d");
        const labels = data.map((item) => item.country || "Unknown");
        const revenues = data.map((item) => item.total_revenue);

        new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Revenue ($)",
                        data: revenues,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Divisional Revenue",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            stepSize: 1,
                        },
                        title: {
                            display: true,
                            text: "Revenue ($)",
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Countries",
                        },
                    },
                },
            },
        });
    }

    function stateWiseRevenueChart(data) {
        const ctx = document
            .getElementById("stateWiseRevenueChart")
            .getContext("2d");
        const labels = data.map((item) => item.state || "Unknown");
        const revenues = data.map((item) => item.total_revenue);

        new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Revenue ($)",
                        data: revenues,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Regional Revenue",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                            stepSize: 1,
                        },
                        title: {
                            display: true,
                            text: "Revenue ($)",
                        },
                    },
                    x: {
                        title: {
                            display: true,
                            text: "Regions",
                        },
                    },
                },
            },
        });
    }

    $(document).ready(function () {
        $("#admin_filter_data").validate({
            rules: {
                franchise: {
                    required: true,
                },
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                let baseURL = window.location.origin;
                let apiEndpoint = "/api/filter-projects";
                let apiUrl = baseURL + apiEndpoint;
                // Serialize form data instead of using FormData for GET requests
                let formData = $(form).serialize();

                $.ajax({
                    url: apiUrl,
                    type: "GET",
                    data: formData, // Correct way to send GET parameters
                    dataType: "json",
                    success: function (response) {
                        // Update statistics boxes
                        $("#totalProjects").text(response.num_projects);
                        $("#completedProjects").text(
                            response.completed_projects
                        );
                        $("#totalRevenue").text(
                            `$ ${response.total_revenue.toLocaleString(
                                undefined,
                                {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                }
                            )}`
                        );
                        $("#completedRevenue").text(
                            `$ ${response.completed_total_revenue.toLocaleString(
                                undefined,
                                {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                }
                            )}`
                        );
                        $("#franchiseeCountry").text(
                            `${response.franchisee_country}`
                        );
                        $("#franchiseeState").text(
                            `${response.franchisee_state}`
                        );

                        updateCompletedProjectChart(response);
                        updateAllProjectChart(response);
                    },
                    error: function (xhr, status, error) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error. Contact your support team!",
                            "danger"
                        );
                    },
                });
            },
        });
    });

    let completedChartInstance = null;
    let allChartInstance = null;
    function updateAllProjectChart(data) {
        let ctx = document.getElementById("allProjectChart").getContext("2d");

        if (allChartInstance) {
            allChartInstance.destroy();
        }

        const labels = data.all_project_chart_data.map((item) => item.name);
        const values = data.all_project_chart_data.map((item) => item.value);
        const bgColors = data.all_project_chart_data.map((item) =>
            item.status === "Completed" ? "#0055a5" : "red"
        );

        allChartInstance = new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Estimated Revenue",
                        data: values,
                        backgroundColor: bgColors,
                        borderColor: "#0055a5",
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: "Revenue ($)" },
                    },
                    x: {
                        title: { display: true, text: "Projects" },
                    },
                },
            },
        });
    }

    function updateCompletedProjectChart(data) {
        let ctx = document
            .getElementById("completedProjectChart")
            .getContext("2d");

        if (completedChartInstance) {
            completedChartInstance.destroy();
        }

        console.log("Data for completed projects chart:", data.completed_project_chart_data);
       
        // Check if completed_project_chart_data exists and is an array
        if (!data.completed_project_chart_data) {
            console.log("No completed_project_chart_data found:", data);
            return;
        }

        if (!Array.isArray(data.completed_project_chart_data)) {
            console.error("completed_project_chart_data is not an array:", typeof data.completed_project_chart_data, data.completed_project_chart_data);
            return;
        }

        if (data.completed_project_chart_data.length === 0) {
            console.log("completed_project_chart_data is empty array");
            return;
        }

        const labels = data.completed_project_chart_data.map(
            (item) => item.name
        );
        const values = data.completed_project_chart_data.map(
            (item) => item.value
        );

        completedChartInstance = new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Completed Project Revenue",
                        data: values,
                        backgroundColor: "#0055a5",
                        borderColor: "#0055a5",
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: { display: true, text: "Revenue ($)" },
                    },
                    x: {
                        title: { display: true, text: "Projects" },
                    },
                },
            },
        });
    }

    //////////admin charts end

    //////////franchisee charts start
    let franchiseeAllProjectChartInstance = null;
    function franchiseAllProjectRevenueChart(data) {
        const ctx = document
            .getElementById("franchiseeAllProjectChart")
            .getContext("2d");

        const labels = data.map((p) => `${p.name} (${p.status})`);
        const values = data.map((p) => p.value);

        if (franchiseeAllProjectChartInstance) {
            franchiseeAllProjectChartInstance.destroy();
        }

        franchiseeAllProjectChartInstance = new Chart(ctx, {
            type: "bar",
            data: {
                labels,
                datasets: [
                    {
                        label: "Project Revenue",
                        data: values,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "All Projects Revenue",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                        },
                    },
                },
            },
        });
    }

    let franchiseeCompletedProjectChartInstance = null;
    function franchiseCompletedProjectRevenueChart(data) {
        const ctx = document
            .getElementById("franchiseeCompletedProjectChart")
            .getContext("2d");

        const labels = data.map((p) => `${p.name} `);
        const values = data.map((p) => p.value);

        if (franchiseeCompletedProjectChartInstance) {
            franchiseeCompletedProjectChartInstance.destroy();
        }

        franchiseeCompletedProjectChartInstance = new Chart(ctx, {
            type: "bar",
            data: {
                labels,
                datasets: [
                    {
                        label: "Project Revenue",
                        data: values,
                        backgroundColor: "#0055a5",
                    },
                ],
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: "Completed Projects Revenue",
                    },
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0,
                        },
                    },
                },
            },
        });
    }

    $(document).ready(function () {
        $("#franchisee_filter_data").validate({
            rules: {},
            submitHandler: function (form, e) {
                e.preventDefault();
                let baseURL = window.location.origin;
                let apiEndpoint = "/api/franchisee/revenue";
                let apiUrl = baseURL + apiEndpoint;
                // Serialize form data instead of using FormData for GET requests
                let formData = $(form).serialize();

                $.ajax({
                    url: apiUrl,
                    type: "GET",
                    data: formData, // Correct way to send GET parameters
                    dataType: "json",
                    success: function (response) {
                        // Update statistics boxes
                        $("#totalProjects").text(response.num_projects);
                        $("#completedProjects").text(
                            response.completed_projects
                        );

                        $("#totalRevenue").text(
                            `$ ${response.total_revenue.toLocaleString(
                                undefined,
                                {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                }
                            )}`
                        );
                        $("#completedRevenue").text(
                            `$ ${response.completed_total_revenue.toLocaleString(
                                undefined,
                                {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                }
                            )}`
                        );

                        updateFranchiseCompletedProjectChart(response);
                        updateFranchiseeAllProjectChart(response);
                    },
                    error: function (xhr, status, error) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error. Contact your support team!",
                            "danger"
                        );
                    },
                });
            },
        });

        let FranchiseCompletedChartInstance = null;
        let franchiseAllChartInstance = null;
        function updateFranchiseeAllProjectChart(data) {
            let ctx = document
                .getElementById("allProjectChart")
                .getContext("2d");

            if (franchiseAllChartInstance) {
                franchiseAllChartInstance.destroy();
            }

            const labels = data.all_project_chart_data.map((item) => item.name);
            const values = data.all_project_chart_data.map(
                (item) => item.value
            );
            const bgColors = data.all_project_chart_data.map((item) =>
                item.status === "Completed" ? "#0055a5" : "red"
            );

            franchiseAllChartInstance = new Chart(ctx, {
                type: "bar",
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: "Estimated Revenue",
                            data: values,
                            backgroundColor: bgColors,
                            borderColor: "#0055a5",
                            borderWidth: 1,
                        },
                    ],
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: "Revenue ($)" },
                        },
                        x: {
                            title: { display: true, text: "Projects" },
                        },
                    },
                },
            });
        }

        function updateFranchiseCompletedProjectChart(data) {
            let ctx = document
                .getElementById("completedProjectChart")
                .getContext("2d");

            if (FranchiseCompletedChartInstance) {
                FranchiseCompletedChartInstance.destroy();
            }

            const labels = data.completed_project_chart_data.map(
                (item) => item.name
            );
            const values = data.completed_project_chart_data.map(
                (item) => item.value
            );

            FranchiseCompletedChartInstance = new Chart(ctx, {
                type: "bar",
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: "Completed Project Revenue",
                            data: values,
                            backgroundColor: "#0055a5",
                            borderColor: "#0055a5",
                            borderWidth: 1,
                        },
                    ],
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: "Revenue ($)" },
                        },
                        x: {
                            title: { display: true, text: "Projects" },
                        },
                    },
                },
            });
        }
    });

    //////////franchisee charts end
}
