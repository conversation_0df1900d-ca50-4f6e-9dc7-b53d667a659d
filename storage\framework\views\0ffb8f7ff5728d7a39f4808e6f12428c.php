
<?php $__env->startSection('title', 'projects'); ?>
<?php $__env->startSection('content'); ?>

<?php
$user = auth()->user();
?>

<?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?>
<div class="redirect_button d-flex w-100 justify-content-end"><a href="/dashboard/project/add">Add Project</a></div>
<?php endif; ?>
<table class="dynamic_datatable table table-striped" style="width:100%">
    <thead>
        <tr>
            <th>Status</th>
            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <th>Franchisee</th>
            <?php endif; ?>
            <th>Attachment</th>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Project Postcode</th>
            <th>Project Value</th>
            <th>Payment Method</th>

            <th>Notes</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Created At</th>
            <?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?> <th class="text-end">Action</th>
            <?php endif; ?>
        </tr>
    </thead>
    <tbody>

        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

        <tr>
            <td>
                <div class="form-check form-switch">
                    <input
                        class="form-check-input project_status_btn"
                        type="checkbox"
                        id="flexSwitchCheckDefault<?php echo e($project->id); ?>"
                        data-id="<?php echo e($project->id); ?>"
                        <?php echo e($project->project_status == 1 ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="flexSwitchCheckDefault"></label>
                </div>
                <span class="badge <?php echo e($project->project_status == 1 ? 'bg-success' : 'bg-danger'); ?>  me-1">
                    <?php echo e($project->project_status == 1 ? 'Completed' : 'Open'); ?>

                </span>
            </td>
            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <td><?php echo e($project->franchisee->franchisee_name); ?></td>
            <?php endif; ?>

            <td>
                <div class="table_image d-flex align-items-center justify-content-center">
                    <?php if($project->attachment): ?>
                    <?php
                    $fileUrl = $project->attachment;
                    $fileExtension = pathinfo($fileUrl, PATHINFO_EXTENSION);
                    $imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
                    $pdfExtensions = ['pdf'];
                    $docExtensions = ['doc','docx','csv'];
                    ?>

                    <?php if(in_array(strtolower($fileExtension), $imageExtensions)): ?>
                    <!-- Image Preview -->
                    <div class="file_preview_hov w-100 h-100 d-flex flex-column align-items-center position-relative">
                        <img class="w-100 h-100 object-fit-contain" src="<?php echo e($fileUrl); ?>" alt="preview image">
                        <button class="position-absolute top-0 left-0 w-100 h-100 d-flex align-items-center justify-content-center" onclick="openPreview('<?php echo e($fileUrl); ?>', 'image')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <?php elseif(in_array(strtolower($fileExtension), $pdfExtensions)): ?>
                    <!-- PDF Preview -->
                    <div class="file_preview_hov w-100 h-100 d-flex flex-column align-items-center position-relative">
                        <embed class="w-100 h-100" src="<?php echo e($fileUrl); ?>" type="application/pdf">
                        <button class="position-absolute top-0 left-0 w-100 h-100 d-flex align-items-center justify-content-center" onclick="openPreview('<?php echo e($fileUrl); ?>', 'pdf')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <?php elseif(in_array(strtolower($fileExtension), $docExtensions)): ?>
                    <!-- DOC/DOCX Link -->
                    <a href="<?php echo e($fileUrl); ?>" target="_blank" class="d-flex align-items-center justify-content-center" onclick="openPreview('<?php echo e($fileUrl); ?>', 'doc')">
                        <i class="fas fa-file-word fa-3x text-primary"></i> Click to View Document
                    </a>
                    <?php else: ?>
                    <!-- Generic File Link -->
                    <a href="<?php echo e($fileUrl); ?>" target="_blank" onclick="openPreview('<?php echo e($fileUrl); ?>', 'file')">View File</a>
                    <?php endif; ?>
                    <?php else: ?>
                    <!-- Default Image -->
                    <img class="w-100 h-100 object-fit-contain" src="/images/dummy.webp" alt="default image">
                    <?php endif; ?>
                </div>

            </td>
            <td><?php echo e($project->first_name); ?></td>
            <td><?php echo e($project->last_name); ?></td>
            <td><?php echo e($project->project_postcode); ?></td>
            <td><?php echo e($project->project_value); ?></td>

            <td><?php echo e($project->payment_type); ?></td>

            <td><?php echo e($project->notes); ?></td>
            <td><?php echo e($project->start_date ? \Carbon\Carbon::parse($project->start_date)->format('d F Y') : ''); ?></td>
             <td><?php echo e($project->end_date ? \Carbon\Carbon::parse($project->end_date)->format('d F Y') : ''); ?></td>
            <td><?php echo e(\Carbon\Carbon::parse($project->created_at)->format('d F Y')); ?></td>

            <?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?>
            <td>
                <div class="action_filter d-flex justify-content-end">
                    <label type="button" id="action_toggle_<?php echo e($project->id); ?>" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                    </label>
                    <div class="action_dropdown_main dropdown-menu" aria-labelledby="action_toggle_<?php echo e($project->id); ?>">
                        <ul class="action_dropdown d-grid w-100">
                            <li class="w-100">
                                <a href="<?php echo e(route('project.update', $project->id)); ?>" class="w-100 d-flex align-items-center">
                                    <i class="fas fa-edit"></i> Update
                                </a>
                            </li>
                            <li class="w-100">
                                <a data-id="<?php echo e($project->id); ?>" class="w-100 d-flex align-items-center project_delete_btn">
                                    <i class="fas fa-trash"></i> Delete
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </td>
            <?php endif; ?>
        </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </tbody>
    <tfoot>
        <tr>
            <th>Status</th>
            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <th>Franchisee</th>
            <?php endif; ?>
            <th>Attachment</th>
            <th>First Name</th>
            <th>Last Name</th>
            <th>Project Postcode</th>
            <th>Project Value</th>
            <th>Payment Method</th>
            <th>Notes</th>
             <th>Start Date</th>
            <th>End Date</th>
            <th>Created At</th>
            <?php if($user && $user->role && ($user->role->role_key === 'franchise')): ?> <th class="text-end">Action</th>
            <?php endif; ?>
        </tr>
    </tfoot>
</table>

<?php echo $__env->make('dashboard/projects/delete', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('dashboard/projects/confirm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('dashboard.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\bathroom ranovator dashboard portal\bathroom-ranovator-dashboard-portal\resources\views/dashboard/projects/index.blade.php ENDPATH**/ ?>