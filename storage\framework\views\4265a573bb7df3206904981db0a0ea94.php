<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <?php echo $__env->make('dashboard/include/head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body>
    <?php echo $__env->make('dashboard/include/alert', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if(session('error')): ?>
    <script>
        window.onload = function() {
            showAlert("<?php echo e(session('error')); ?>", "danger");
        };
    </script>
    <?php endif; ?>

    <?php echo $__env->make("dashboard.include.sidebar", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <div class="content_section d-flex flex-column">
        <?php echo $__env->make("dashboard.include.header", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="content_section_main w-100">
            <div class="content_container">
                <div class="content_body">
                    <?php echo $__env->yieldContent("content"); ?>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('dashboard/mediaPopup', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<!-- Bootstrap 5 File Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalModalLabel" aria-hidden="true">
    <div class="modal-dialog confirm_modal">
        <div class="modal-content">
            <div class="modal-header flex-column">
                <div class="icon-box">
                    <i class="fas fa-eye"></i> <!-- Changed to eye icon for preview -->
                </div>
                <h4 class="modal-title w-100">File Preview</h4>
                <a class="close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="fas fa-times"></i>
                </a>
            </div>
            <div class="modal-body text-center" id="previewContainer">
                <!-- Content will be dynamically inserted here -->
            </div>
            <div class="modal-footer justify-content-center">
                <a type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</a>
            </div>
        </div>
    </div>
</div>


</body>


</html><?php /**PATH D:\clients project\bathroom ranovator dashboard portal\bathroom-ranovator-dashboard-portal\resources\views/dashboard/include/layout.blade.php ENDPATH**/ ?>