<div class="dashboard_sidebar d-flex flex-column position-fixed overflow-y-auto">
    <div class="sidebar_control_btn control_btn justify-content-end mb-4 mr-4">
        <i class="sidebar_toggle_btn fa-solid fa-bars"></i>
    </div>
    <div class="sidebar_logo d-flex w-100 align-items-center justify-content-center">
        <img src="/images/logo.png">
    </div>



    <div class="filters_accordion_parent d-grid w-100">

        <ul class="filters_listing d-flex flex-column w-100 align-items-start overflow-y-auto">


            <?php
            $user = auth()->user();
            ?>


            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/home') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/home')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Dashboard</a>
            </li>

            <?php if($user && $user->role && ($user->role->role_key === 'franchise' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/leaderboard') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/leaderboard')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Leaderboard</a>
            </li>

            <?php endif; ?>

            <?php if($user && $user->role && ($user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>



            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/country') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/country')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Division Management</a>
            </li>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/states') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/states')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Regional Management
                </a>
            </li>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/franchisee') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/franchisee')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Franchisee Management</a>
            </li>
            <?php endif; ?>



            <?php if($user && $user->role && ($user->role->role_key === 'franchise' || $user->role->role_key === 'admin' || $user->role->role_key === 'super_admin')): ?>
            <li
                class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/projects') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/dashboard/projects')); ?>"
                    class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Project Managment</a>
            </li>

            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/leads') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/leads')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Leads Tracker</a>
            </li>

            <?php endif; ?>



            <?php if($user && $user->role && ($user->role->role_key === 'admin')): ?>
            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/franchisee/prospects') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/franchisee/prospects')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Franchisee Prospects</a>
            </li>
            <?php endif; ?>

            <?php if($user && $user->role && ($user->role->role_key === 'super_admin')): ?>
            <li class="d-flex align-items-center flex-wrap w-100 <?php echo e(Request::is('dashboard/media') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('dashboard/media')); ?>" class="d-flex align-items-center w-100 position-relative"><span
                        class="bullet position-relative"></span>Media</a>
            </li>
            <?php endif; ?>
        </ul>




    </div>


</div><?php /**PATH D:\clients project\bathroom ranovator dashboard portal\bathroom-ranovator-dashboard-portal\resources\views/dashboard/include/sidebar.blade.php ENDPATH**/ ?>